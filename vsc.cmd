help refactor `PlaceNextOrder` so that it will place Order at most one for 350ms

use ```dotnet build "D:\work\xstarwalker168\Python\Finance\QuantConnectLean\TradingSolution\Lean\QuantConnect.Lean.sln" --configuration Debug ``` to check any compiling errors

use ```cd "D:\work\xstarwalker168\Python\Finance\QuantConnectLean\TradingSolution\Lean\Launcher\bin\Debug; .\QuantConnect.Lean.Launcher.exe --config D:\work\xstarwalker168\Python\Finance\QuantConnectLean\trading-bot-config.json --parameters tradeMode:2,symbol:SUI,currency:USDT,leverage:5 --results-destination-folder "D:/work/xstarwalker168/Python/Finance/QuantConnectLean/QC-Log-Dir" --algorithm-language CSharp --environment live-mexc --algorithm-location cCryptoBot.dll --data-folder "D:/work/xstarwalker168/Python/Finance/QuantConnectLean/Data"

``` to check runtime results

if place Order failed:
```
{
  "success": false,
  "code": 510,
  "message": "Request frequently too fast!"
}
```

we should retry, so you need to refactor related code to pass the RAW JSON to cCryptoBot.cs and get the error code